{"name": "vite-vue-ts", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:test": "vite build --mode test", "build:prod": "vite build --mode prod"}, "dependencies": {"@deck.gl/layers": "^9.0.34", "@deck.gl/mapbox": "^9.0.34", "@easydarwin/easyplayer": "^5.1.1", "@fingerprintjs/fingerprintjs": "^4.5.1", "@mapbox/mapbox-gl-draw": "^1.4.3", "@turf/turf": "^7.1.0", "@vueuse/core": "^10.11.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.7", "dayjs": "^1.11.13", "deck.gl": "^9.0.34", "echarts": "^5.5.1", "gcoord": "^1.0.6", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash.clonedeep": "^4.5.0", "mapbox-gl": "^3.7.0", "minio-vite-js": "^0.0.6", "naive-ui": "^2.40.1", "pinia": "^2.2.5", "pinia-plugin-persistedstate": "^3.2.3", "sortablejs": "^1.15.6", "video.js": "^8.19.1", "vue": "^3.5.12", "vue-router": "^4.4.5", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@iconify/json": "^2.2.266", "@iconify/utils": "^2.1.33", "@iconify/vue": "^4.1.2", "@types/node": "^20.17.3", "@unocss/preset-rem-to-px": "^0.59.4", "@unocss/transformer-attributify-jsx": "^0.59.4", "@unocss/transformer-directives": "0.59.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue-macros/reactivity-transform": "^0.4.8", "glob": "^10.4.5", "prettier": "^3.3.3", "sass": "^1.80.5", "terser": "^5.36.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "unocss": "^0.60.4", "unplugin-auto-import": "^0.17.8", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}