<template>
  <div class="map-style">
    <div
      class="style-item item-common"
      v-for="(el, idx) in mapStyles.filter(el => el.label !== activeMapStyle)"
      :style="{
        backgroundImage: `url(${el.bg})`,
        display: 'none',
      }"
      @click="onStyleItemClick(el, idx)"
    >
      <div class="label">{{ el.label }}</div>
    </div>

    <div
      class="item-common b-(2px solid #ffffff)"
      :style="{ backgroundImage: `url(${mapStyles.find(el => el.label === activeMapStyle)?.bg})` }"
    >
      <div class="label">{{ activeMapStyle }}</div>
    </div>
  </div>
</template>
<script lang="jsx">
  import { switchMapStyle, clearSourceAndLayer } from '@/utils/mapUtils.js'

  export default {
    name: 'MapStyle',
    props: ['mapIns', 'activeStyle'],
    data() {
      return {
        activeMapStyle: this.activeStyle || '卫星图',
        mapStyles: [
          {
            label: '地形',
            bg: require('@/assets/images/map-terrain.png'),
            setBaseMapStyle: () => {
              clearSourceAndLayer(this.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

              this.mapIns.addLayer(
                {
                  id: 'mapbox-wmts-base-layer',
                  type: 'raster',
                  source: {
                    type: 'raster',
                    tiles: [
                      `${process.env.VUE_APP_TIANDI_BASE}/ter_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=ter&tilematrix={z}&tilerow={y}&tilecol={x}`,
                    ],
                    tileSize: 256,
                  },
                },
                'mapbox-wmts-label-layer',
              )
            },
          },
          {
            label: '卫星图',
            bg: require('@/assets/images/map-satellite.png'),
            setBaseMapStyle: () => {
              // switchMapStyle(this.mapIns, 'mapbox/satellite-v9')

              clearSourceAndLayer(this.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

              this.mapIns.addLayer(
                {
                  id: 'mapbox-wmts-base-layer',
                  type: 'raster',
                  source: {
                    type: 'raster',
                    tiles: [
                      `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
                    ],
                    tileSize: 256,
                  },
                },
                'mapbox-wmts-label-layer',
              )
            },
          },
          {
            label: '地图',
            bg: require('@/assets/images/map-street.png'),
            setBaseMapStyle: () => {
              // switchMapStyle(this.mapIns, 'zhuangqg/clgogvvbx002p01pz7yjnb6cf')

              clearSourceAndLayer(this.mapIns, ['mapbox-wmts-base-layer'], ['mapbox-wmts-base-layer'])

              this.mapIns.addLayer(
                {
                  id: 'mapbox-wmts-base-layer',
                  type: 'raster',
                  source: {
                    type: 'raster',
                    tiles: [
                      `${process.env.VUE_APP_TIANDI_BASE}/vec_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=vec&tilematrix={z}&tilerow={y}&tilecol={x}`,
                    ],
                    tileSize: 256,
                  },
                },
                'mapbox-wmts-label-layer',
              )
            },
          },
        ],
      }
    },

    mounted() {
      this.onMapStyleMounted()
    },
    methods: {
      onStyleItemClick(el, idx) {
        this.activeMapStyle = el.label

        el.setBaseMapStyle()
      },
      onMapStyleMounted() {
        this.mapIns.addLayer(
          {
            id: 'mapbox-wmts-label-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${process.env.VUE_APP_TIANDI_BASE}/cva_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cva&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          this.mapIns.getStyle().layers[0].id,
        )

        this.onStyleItemClick(this.mapStyles[this.mapStyles.findIndex(el => el.label === this.activeMapStyle)])
      },
    },
  }
</script>

<style lang="scss" scoped>
  .map-style {
    z-index: 99;
    border-radius: 4px;
    display: flex;

    padding: 2px;
    color: #ffffff;

    &:hover {
      &::before {
        position: absolute;
        content: '';
        border-radius: 4px;
        width: calc(100% + 20px);
        height: calc(100% + 20px);
        background: rgba(255, 255, 255, 0.8);
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      .style-item {
        display: block !important;
      }
    }

    .item-common {
      width: 108px;
      height: 66px;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-radius: 4px;
      user-select: none;
      cursor: pointer;

      .label {
        position: absolute;
        right: 0;
        bottom: 0;
        font-size: 12px;
        line-height: 12px;
        padding: 4px;
        background-color: #1890ff;
        border-radius: 2px;
      }
    }

    .style-item {
      margin-right: 10px;
      border: 2px solid #ffffff;
    }
  }
</style>
