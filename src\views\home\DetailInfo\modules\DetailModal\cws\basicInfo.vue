<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">农村供水工程代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">农村供水工程名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="农村供水工程经度(°)">
              <div class="form-value">{{ data.longitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="农村供水工程维度(°)">
              <div class="form-value">{{ data.latitude }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="农村供水工程所在位置"
              :labelCol="{ span: 3 }"
              :wrapperCol="{ span: 13 }"
              class="note"
            >
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="农村供水工程所在位置">
              <div class="form-value">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程类型">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engType"
                placeholder="请选择"
                :options="engTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engTypeOptions.find(el => el.value == data.engType)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="供水方式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wasuType"
                placeholder="请选择"
                :options="wasuTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ wasuTypeOptions.find(el => el.value == data.wasuType)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计供水规模(m³/d)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desWasuScal"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desWasuScal }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计供水人口(万人)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desWasuPop"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desWasuPop }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="供水范围">
              <a-input v-if="type == 'edit'" v-model="form.wasuRang" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.wasuRang }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getCws, updateCws } from './services'
  import moment from 'moment'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        loading: false,
        data: {},

        wasuTypeOptions: [], //供水方式
        engTypeOptions: [], //工程类型
        engStatOptions: [], //工程建设情况

        type: 'detail',
        form: {
          compDate: '',
          desWasuPop: undefined,
          desWasuScal: undefined,
          engStat: '',
          engType: '',
          id: undefined,
          note: '',
          projectId: undefined,
          startDate: '',
          wasuRang: '',
          wasuType: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('wasuType').then(res => {
          this.wasuTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('cwsEngType').then(res => {
          this.engTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.compYear = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      getDataSource() {
        getCws({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            engType: res.data.engType ? `${res.data.engType}` : undefined,
            wasuType: res.data.wasuType ? `${res.data.wasuType}` : undefined,
            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateCws(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
