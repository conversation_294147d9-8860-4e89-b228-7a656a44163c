<template>
  <div class="basic-info">
    <div class="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">
            基本信息
            <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
              {{ this.type == 'detail' ? '编辑' : '确定' }}
            </a-button>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">斗门代码：</span>
            <span class="value">{{ data?.projectCode }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">斗门名称：</span>
            <span class="value">{{ data?.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">行政区划：</span>
            <span class="value">{{ data?.districtFullName }}</span>
          </div>
        </a-col>
        <!-- <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">斗门所在位置：</span>
            <span class="value" :title="data.location">{{ data?.location }}</span>
          </div>
        </a-col> -->

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">斗门经度(°)：</span>
            <span class="value">{{ data?.longitude }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">斗门维度(°)：</span>
            <span class="value">{{ data?.latitude }}</span>
          </div>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="斗门所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
            <div class="form-value" :title="data.location">{{ data.location }}</div>
          </a-form-model-item>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title second-title">主要特征信息</div>
        </a-col>

        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          layout="horizontal"
          :labelCol="{ span: 12 }"
          :wrapperCol="{ span: 12 }"
          labelAlign="right"
        >
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="闸门型式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.gateForm"
                placeholder="请选择"
                :options="gateFormOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ gateFormOptions.find(el => el.value == data.gateForm)?.label }}
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="尺寸">
              <a-input v-if="type == 'edit'" v-model="form.size" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.size }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="角度(°)">
              <a-input-number
                v-if="type == 'edit'"
                :precision="2"
                v-model="form.angle"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.angle }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="桩号">
              <a-input v-if="type == 'edit'" v-model="form.stakeMark" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.stakeMark }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计流量(m³/s)">
              <a-input-number
                v-if="type == 'edit'"
                :precision="5"
                v-model="form.designFlow"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.designFlow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="改造年份">
              <!-- <a-input-number
                v-if="type == 'edit'"
                v-model="form.reformYear"
                style="width: 100%"
                placeholder="请输入"
              /> -->
              <a-date-picker
                mode="year"
                format="YYYY"
                v-if="type == 'edit'"
                v-model="form.reformYear"
                placeholder="请选择"
                allow-clear
                style="width: 100%"
                :open="yearShowOne"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
              <div v-else class="form-value">{{ data.reformYear }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="已运行年限">
              <a-input-number
                v-if="type == 'edit'"
                :precision="0"
                v-model="form.runYear"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.runYear }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="现状评估结论">
              <a-input v-if="type == 'edit'" v-model="form.evalConclusion" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.evalConclusion }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-form-model>
      </a-row>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDoor, updateDoor } from './services'
  import moment from 'moment'

  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        yearShowOne: false, //年份
        loading: false,
        data: {},
        gateFormOptions: [],

        engTaskOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],
        engStatOptions: [],
        admDepOptions: [],

        sluiceTypeOptions: [],
        sluicePurposeOptions: [],
        type: 'detail',
        form: {
          angle: undefined,
          designFlow: undefined,
          evalConclusion: '',
          gateForm: '',
          id: undefined,
          note: '',
          projectId: undefined,
          reformYear: '',
          runYear: undefined,
          size: '',
          stakeMark: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()

      this.getDataSource()
    },
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.reformYear = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      init() {
        //闸门型式
        getOptions('gateForm').then(res => {
          this.gateFormOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getDoor({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,

            gateForm: res.data.gateForm ? `${res.data.gateForm}` : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }
          updateDoor(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
