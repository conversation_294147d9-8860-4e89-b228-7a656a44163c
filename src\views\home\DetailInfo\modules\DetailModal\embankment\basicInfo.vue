<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">堤防代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">堤防名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">行政区划：</span>
              <span class="value">{{ data?.districtFullName }}</span>
            </div>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)" prop="startLong">
              <a-input-number v-if="type == 'edit'" v-model="form.startLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)" prop="startLat">
              <a-input-number v-if="type == 'edit'" v-model="form.startLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)" prop="endLong">
              <a-input-number v-if="type == 'edit'" v-model="form.endLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)" prop="endLat">
              <a-input-number v-if="type == 'edit'" v-model="form.endLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :span="16">
            <a-form-model-item label="起点所在位置" :labelCol="{ span: 5 }" :wrapperCol="{ span: 11 }" class="location">
              <a-input
                v-if="type == 'edit'"
                class="area-text"
                v-model="form.startLoc"
                placeholder="请输入"
                allowClear
              />
              <div v-else class="form-value" :title="data.startLoc">{{ data.startLoc }}</div>
            </a-form-model-item>
          </a-col> -->

          <a-col :span="24">
            <a-form-model-item label="起点所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-input
                v-if="type == 'edit'"
                class="area-text"
                v-model="form.startLoc"
                placeholder="请输入"
                allowClear
              />
              <div v-else class="form-value" :title="data.startLoc">{{ data.startLoc }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="终点所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-input
                v-if="type == 'edit'"
                class="area-text"
                v-model="form.startLoc"
                placeholder="请输入"
                allowClear
              />
              <div v-else class="form-value" :title="data.endLoc">{{ data.endLoc }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防级别" prop="dikeGrad">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.dikeGrad"
                placeholder="请选择"
                :options="dikeGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ dikeGradOptions.find(el => el.value == data.dikeGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防类型" prop="dikeType">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.dikeType"
                placeholder="请选择"
                :options="dikeTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ dikeTypeOptions.find(el => el.value == data.dikeType)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="堤防型式" prop="dikePatt">
              <a-select
                v-if="type == 'edit'"
                allowClear
                :filter-option="filterOption"
                v-model="form.dikePatt"
                mode="multiple"
                :maxTagCount="1"
                placeholder="请选择"
                :options="dikePattOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{
                  dikePattOptions
                    .filter(item => data.dikePatt?.includes(item.value))
                    .map(item => item.label)
                    .join(', ')
                }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防长度(m)" prop="dikeLen">
              <a-input-number v-if="type == 'edit'" v-model="form.dikeLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.dikeLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防起点桩号" prop="dikeStartNum">
              <a-input v-if="type == 'edit'" v-model="form.dikeStartNum" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.dikeStartNum }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防终点桩号" prop="dikeEndNum">
              <a-input v-if="type == 'edit'" v-model="form.dikeEndNum" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.dikeEndNum }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="高程系统" prop="elevSys">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.elevSys"
                placeholder="请选择"
                :options="elevSysOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ elevSysOptions.find(el => el.value == data.elevSys)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点堤顶高程(m)" prop="startDikeTopEl">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.startDikeTopEl"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.startDikeTopEl }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点堤顶高程(m)" prop="endDikeTopEl">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.endDikeTopEl"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.endDikeTopEl }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防最小高度(m)" prop="dikeHeigMin">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.dikeHeigMin"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.dikeHeigMin }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤防最大高度(m)" prop="dikeHeigMax">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.dikeHeigMax"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.dikeHeigMax }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤顶最大宽度(m)" prop="dikeTopWidMax">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.dikeTopWidMax"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.dikeTopWidMax }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="堤顶最小宽度(m)" prop="dikeTopWidMin">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.dikeTopWidMin"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.dikeTopWidMin }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程任务" prop="engTask">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engTask"
                :filter-option="filterOption"
                mode="multiple"
                placeholder="请选择"
                :options="engTaskOptions"
                :maxTagCount="1"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{
                  engTaskOptions
                    .filter(item => data.engTask?.includes(item.value))
                    .map(item => item.label)
                    .join(', ')
                }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况" prop="engStat">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDike, updateDike } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        pumpTypeOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],

        engTaskOptions: [], //工程任务
        engStatOptions: [], //工程建设情况
        admDepOptions: [], //归口管理部门

        elevSysOptions: [], //高程系统
        dikeTypeOptions: [], //堤防类型
        dikePattOptions: [], //堤防型式
        dikeGradOptions: [], //堤防级别

        type: 'detail',
        form: {
          admDep: '',
          compDate: '',
          dikeEndNum: '',
          dikeGrad: '',
          dikeHeigMax: undefined,
          dikeHeigMin: undefined,
          dikeLen: undefined,
          dikePatt: '',
          dikeStartNum: '',
          dikeTopWidMax: undefined,
          dikeTopWidMin: undefined,
          dikeType: '',
          elevSys: '',
          endDikeTopEl: undefined,
          endLat: '',
          endLoc: '',
          endLong: '',
          engStat: '',
          engTask: '',
          id: undefined,
          note: '',
          projectId: undefined,
          startDate: '',
          startDikeTopEl: undefined,
          startLat: '',
          startLoc: '',
          startLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('elevationSystem').then(res => {
          this.elevSysOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('dikeType').then(res => {
          this.dikeTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('dikePatt').then(res => {
          this.dikePattOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('dikeWait').then(res => {
          this.dikeGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        // 归口管理部门-admDep
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        //工程任务 engTaskOptions
        getOptions('dikeTask').then(res => {
          this.engTaskOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getDike({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            engTask: res.data?.engTask ? updateListFromStr(res.data?.engTask) : undefined,
            dikePatt: res.data?.dikePatt ? updateListFromStr(res.data?.dikePatt) : undefined,

            dikeGrad: res.data.dikeGrad ? `${res.data.dikeGrad}` : undefined,
            dikeType: res.data.dikeType ? `${res.data.dikeType}` : undefined,
            elevSys: res.data.elevSys ? `${res.data.elevSys}` : undefined,
            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,
            admDep: res.data.admDep ? `${res.data.admDep}` : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.engTask = this.form.engTask ? this.form.engTask.join(',') : ''
          this.form.dikePatt = this.form.dikePatt ? this.form.dikePatt.join(',') : ''
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateDike(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
