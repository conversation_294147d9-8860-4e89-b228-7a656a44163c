<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    :modalWidth="windowSize.width"
    :modalHeight="windowSize.height"
    @cancel="cancel"
    ref="lineParamSetModalRef"
  >
    <div slot="content" layout="vertical" class="container">
      <div class="left">
        <div class="header">基本信息</div>
        <a-form-model
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          style="padding: 10px"
          labelAlign="right"
        >
          <a-form-model-item label="任务类型">
            <div style="line-height: 28px">{{ form.isTemp == 0 ? '计划任务' : '临时任务' }}</div>

            <!-- <a-select v-model="form.isTemp" allowClear class="formItemValue" disabled>
              <a-select-option v-for="(d, index) in isTempOptions" :key="index" :value="d.key" :disabled="d.key == 0">
                {{ d.value }}
              </a-select-option>
            </a-select> -->
          </a-form-model-item>
          <a-form-model-item label="任务名称" prop="taskName">
            <a-input :disabled="isDetail" allowClear v-model="form.taskName" placeholder="请输入" style="width: 100%" />
          </a-form-model-item>
          <a-form-model-item label="巡检范围" prop="lineId">
            <a-select
              :disabled="isDetail"
              v-model="form.lineId"
              placeholder="请选择"
              style="width: 100%"
              @change="onLineChange"
            >
              <a-select-option v-for="(d, index) in lineOptions" :key="index" :value="d.lineId">
                {{ d.lineName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="巡检班次" prop="shiftId">
            <a-select
              :disabled="isDetail"
              v-model="form.shiftId"
              placeholder="请选择"
              style="width: 100%"
              @change="onWorkShiftChange"
            >
              <a-select-option v-for="(d, index) in workShiftList" :key="index" :value="d.shiftId">
                {{ d.shiftName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="巡检班组" prop="groupId">
            <a-select
              :disabled="isDetail"
              v-model="form.groupId"
              placeholder="请选择"
              style="width: 100%"
              @change="onWorkGroupChange"
            >
              <a-select-option v-for="(d, index) in workGroupList" :key="index" :value="d.groupId">
                {{ d.groupName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="巡检人">
            <a-select
              :disabled="isDetail"
              v-model="form.patrolUserId"
              allowClear
              placeholder="请选择"
              style="width: 100%"
            >
              <a-select-option v-for="(d, index) in workGroupUserList" :key="index" :value="d.userId">
                {{ d.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="开始时间" prop="planStartTime">
            <a-date-picker
              :disabled="isDetail"
              v-model="form.planStartTime"
              :show-time="{ format: 'HH:mm' }"
              valueFormat="YYYY-MM-DD HH:mm"
              placeholder="请选择"
              style="width: 100%"
            />
          </a-form-model-item>
          <a-form-model-item label="结束时间" prop="planEndTime">
            <a-date-picker
              :disabled="isDetail"
              v-model="form.planEndTime"
              :show-time="{ format: 'HH:mm' }"
              valueFormat="YYYY-MM-DD HH:mm"
              placeholder="请选择"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="right">
        <Container
          v-if="form.lineId && lineInfo"
          ref="containerRef"
          :rowInfo="lineInfo"
          :objects="objects"
          :type="objType"
        />
      </div>
    </div>

    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="confirm" v-if="!isDetail" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Container from './container'

  import {
    getPatrolObjectList,
    addPatrolTask,
    updatePatrolTask,
    getPatrolTask,
    chooseObjectPatrolLine,
    getWorkGroupUserList,
    getWorkGroupList,
    getRunDetails,
  } from './services'
  import moment from 'moment'

  export default {
    name: 'LineParamSetModal',
    props: ['lineOptions', 'workShiftList'],
    components: {
      AntModal,
      Container,
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '',
        isDetail: false,
        rowInfo: null,
        objects: [],
        lineInfo: null,
        objType: 'area',
        windowSize: {
          width: `${parseInt(window.innerWidth * 0.95)}`,
          height: `${parseInt(window.innerHeight * 0.96)}`,
        },

        isTempOptions: [
          { key: 0, value: '计划任务' },
          { key: 1, value: '临时任务' },
        ],
        workGroupList: [],
        workGroupUserList: [],

        form: {
          taskId: undefined,
          patrolType: this.$route.meta.query.patrolType,
          taskName: undefined,
          lineId: undefined,
          shiftId: undefined,
          groupId: undefined,
          patrolUserId: undefined,
          planStartTime: null,
          planEndTime: null,

          taskObjectItems: [
            {
              itemIds: [],
              objectId: null,
            },
          ],
        },
        rules: {
          taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
          lineId: [{ required: true, message: '巡检范围不能为空', trigger: 'change' }],
          shiftId: [{ required: true, message: '巡检班次不能为空', trigger: 'change' }],
          groupId: [{ required: true, message: '巡检班组不能为空', trigger: 'change' }],
          planStartTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
          planEndTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      handleAdd() {
        this.modalTitle = '新增'
        this.open = true
        this.form.shiftId = this.workShiftList[0].shiftId
        this.onWorkShiftChange(this.form.shiftId)
        this.$nextTick(() => {
          setTimeout(() => {
            this.form.groupId = this.workGroupList[0]?.groupId
            this.onWorkGroupChange(this.form.groupId)
          }, 300)
        })
      },
      handleUpdate(record) {
        this.modalTitle = '修改'
        this.open = true
        this.rowInfo = record

        this.modalLoading = true
        this.form = JSON.parse(JSON.stringify(record))

        getRunDetails({ taskId: record.taskId }).then(res => {
          this.objects = res?.data || []
          this.modalLoading = false
          this.objType = 'task'
          let obj = this.lineOptions.find(el => el.lineId == record.lineId)
          this.lineInfo = {
            ...obj,
            lineRange: JSON.parse(obj.lineRange),
          }
        })

        this.onLineChange(record.lineId)
        this.onWorkShiftChange(record.shiftId, 'update')
        this.onWorkGroupChange(record.groupId)
      },

      handleDetail(record) {
        this.modalTitle = '详情'
        this.open = true
        this.rowInfo = record
        this.form = JSON.parse(JSON.stringify(record))

        this.isDetail = true
        this.modalLoading = true

        getRunDetails({ taskId: record.taskId }).then(res => {
          this.objects = res?.data || []
          this.objType = 'task'
          this.modalLoading = false
          let obj = this.lineOptions.find(el => el.lineId == record.lineId)
          this.lineInfo = {
            ...obj,
            lineRange: JSON.parse(obj.lineRange),
          }
        })

        this.onLineChange(record.lineId)
        this.onWorkShiftChange(record.shiftId)
        this.onWorkGroupChange(record.groupId)
      },

      onLineChange(val) {
        if (this.rowInfo) {
          if (this.rowInfo.lineId == val) {
            this.objType = 'task'
          } else {
            this.objType = 'area'
          }
        }

        let obj = this.lineOptions.find(el => el.lineId == val)
        if (obj) {
          this.lineInfo = {
            ...obj,
            lineRange: JSON.parse(obj.lineRange),
          }
        }
      },

      onWorkShiftChange(val, type) {
        this.workGroupList = []
        getWorkGroupList({ shiftId: val }).then(res => {
          this.workGroupList = res?.data[0]?.groups
          this.form.groupId = this.workGroupList[0]?.groupId
          if (type === 'update') {
            this.form.patrolUserId = this.rowInfo.patrolUserId || undefined
          } else {
            this.form.patrolUserId = undefined
          }
          this.onWorkGroupChange(this.form.groupId)
        })
      },

      onWorkGroupChange(val) {
        this.workGroupUserList = []
        let paramWorkGroupUser = {
          deptId: null,
          groupId: val,
          name: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          postId: null,
          shiftId: this.form.shiftId,
        }
        getWorkGroupUserList(paramWorkGroupUser).then(res => {
          this.workGroupUserList = res?.data?.data || []
        })
      },

      cancel() {
        this.open = false
        this.$emit('close')
      },

      confirm() {
        this.$refs.formRef.validate(valid => {
          if (valid) {
            this.loading = true
            const queryParam = {
              ...this.form,
              planStartTime: this.form.planStartTime,
              planEndTime: this.form.planEndTime,
            }

            this.$refs.containerRef.confirm(params => {
              if (this.form.taskId) {
                updatePatrolTask({
                  ...queryParam,
                  taskObjectItems: params.objects,
                })
                  .then(res => {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.loading = false
                    this.$emit('close')
                    this.$emit('ok')
                  })
                  .catch(() => (this.loading = false))
              } else {
                addPatrolTask({
                  ...queryParam,
                  taskObjectItems: params.objects,
                })
                  .then(res => {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.loading = false
                    this.$emit('close')
                    this.$emit('ok')
                  })
                  .catch(() => (this.loading = false))
              }
            })
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }

  .container {
    width: 100%;
    height: 100%;
    display: flex;

    .left {
      width: 26%;
      background-color: #fff;
      border-radius: 2px;
      margin-right: 10px;

      .header {
        background-color: #1890ff;
        border-radius: 2px 2px 0 0;
        line-height: 20px;
        color: #fff;
        padding: 10px;
      }
    }
    .right {
      flex: 1;
    }
  }
  ::v-deep .ant-form-item {
    margin-bottom: 13px;
  }
  ::v-deep .ant-form-item-with-help {
    margin-bottom: 0px;
  }
</style>
