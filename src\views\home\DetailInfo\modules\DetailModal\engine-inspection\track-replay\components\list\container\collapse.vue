<template>
  <div class="collapse-panel">
    <!-- 统计数 -->
    <div class="project-statistics">
      <div class="project-data">
        <p class="project-title">巡检对象数</p>
        <p class="project-num">{{ collapseData.length }}个</p>
      </div>
      <div class="project-data">
        <p class="project-title">设备数</p>
        <p class="project-num">{{ deviceNum }}个</p>
      </div>
      <div class="project-data">
        <p class="project-title">巡检项数</p>
        <p class="project-num">{{ selectRow.length }}个</p>
      </div>
    </div>

    <div class="collapse-content">
      <a-collapse v-model="activeKey">
        <a-collapse-panel v-for="(item, index) in collapseData" :key="String(index)">
          <div slot="header" class="collapse-header">
            <div class="header-left">
              <span class="order">
                {{ index + 1 }}
              </span>
              <div class="name" :title="item.objectName">{{ item.objectName }}</div>
              <span>
                巡检项：已选{{ item.checkRowKeys.length }}个 未选{{
                  item.dataSource.length - item.checkRowKeys.length
                }}个
              </span>
            </div>
            <a-icon class="delete" type="delete" @click.stop="delPatrolObject(item)" />
          </div>

          <VxeTable
            ref="vxeTableRef"
            :isShowTableHeader="false"
            max-height="600"
            :autoHeight="true"
            size="mini"
            :columns="columns"
            :tableData="item.dataSource"
            :tablePage="false"
            :checkbox-config="{ checkRowKeys: item.checkRowKeys, highlight: false, showHeader: true }"
            :row-config="{ isHover: true, keyField: 'id' }"
            @selectChange="$listeners.handleSelectRow"
            :isDataChangeReload="true"
          ></VxeTable>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  const objectOptions = [
    { label: '水利工程', value: 1 },
    { label: '监测站点', value: 2 },
    { label: '江河湖泊', value: 3 },
    { label: '站点终端', value: 8 },
    { label: '工程设备', value: 9 },
  ]

  export default {
    name: 'CollapsePanel',
    props: {
      collapseData: { default: () => [] },
      selectRow: { default: () => [] },
    },
    components: { VxeTable },
    data() {
      return {
        activeKey: [],
        columns: [
          { type: 'checkbox', width: 30 },
          {
            title: '对象类型',
            field: 'objectType',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return objectOptions.find(el => el.value == row.objectType).label
              },
            },
          },
          { title: '对象编码', field: 'objectCode', minWidth: 100 },
          { title: '对象名称', field: 'objectName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '巡检编码', field: 'itemId', minWidth: 100, visible: false },
          { title: '巡检项', field: 'itemName', minWidth: 100 },
          { title: '所在位置', field: 'address', minWidth: 100, showOverflow: 'tooltip' },
        ],
      }
    },
    computed: {
      deviceNum() {
        return [
          ...new Set(this.selectRow.filter(el => el.objectType === 8 || el.objectType === 9).map(el => el.objectId)),
        ]?.length
      },
    },
    watch: {
      collapseData: {
        handler(newVal, oldVal) {},
        deep: true,
      },
    },
    created() {},
    mounted() {},
    methods: {
      delPatrolObject(row) {
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + row.objectName + '"的数据',
          onOk: () => {
            this.$emit('handleDelete', row)

            this.$message.success(`删除成功`, 3)
          },
          onCancel() {},
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

  .collapse-panel {
    height: 100%;
    margin: 0 10px 10px;

    .project-statistics {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;

      .project-data {
        flex: 0 0 30%;
        background-color: #1890ff;
        color: #fff;
        height: 60px;
        border-radius: 2px;

        .project-title {
          float: left;
          margin-left: 15px;
          margin-top: 20px;
        }

        .project-num {
          float: right;
          margin-right: 15px;
          margin-top: 20px;
        }
      }
    }

    .collapse-content {
      margin-top: 15px;
      overflow-y: auto;
      height: calc(100% - 137px);
      .collapse-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left {
          display: flex;
          align-items: center;

          .order {
            background-color: #1890ff;
            color: #fff;
            font-size: 12px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 4px;
          }
          .name {
            margin-left: 10px;
            width: 190px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .delete {
          color: red;
        }
      }
    }
  }
</style>
