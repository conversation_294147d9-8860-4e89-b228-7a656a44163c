<template>
  <div class="detail-content">
    <section class="section-item" style="margin-right: 10px">
      <a-tabs size="small" v-model="tabVal" @change="onTabChange">
        <a-tab-pane key="1" tab="未选">
          <Table
            @getObjectPageList="getObjectPageList"
            list=""
            :chooseObject="chooseObject"
            :rowInfo="rowInfo"
            @handleJoin="handleJoin"
            forceRender
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="已选">
          <Collapse
            :collapseData="collapseData"
            forceRender
            :selectRow="selectRow"
            @handleSelectRow="handleSelectRow"
            @handleDelete="handleDelete"
          />
        </a-tab-pane>
      </a-tabs>
    </section>

    <section class="section-item">
      <Map :rowInfo="rowInfo" :allObjectPageList="allObjectPageList" />
    </section>
  </div>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Table from './table.vue'
  import Map from './map.vue'
  import Collapse from './collapse.vue'
  import { getLine, getChooseObject, getPatrolObjectPage } from '../../../services'

  export default {
    name: 'Container',
    props: ['rowInfo', 'type', 'objects'],
    components: {
      AntModal,
      Table,
      Map,
      Collapse,
    },
    data() {
      return {
        tabVal: '1',
        windowSize: {
          width: `${parseInt(window.innerWidth * 0.95)}`,
          height: `${parseInt(window.innerHeight * 0.96)}`,
        },

        allObjectPageList: [],
        chooseObject: [],
        collapseData: [],
        selectRow: [],
      }
    },
    created() {},
    computed: {},
    watch: {
      rowInfo: {
        handler(newVal, oldVal) {
          if (this.type == 'task') {
            let arr = this.dealObjData(this.objects)
            this.handleJoin(null, arr)
          } else {
            this.getDetailLine(newVal)
          }
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      // 全部对象列表
      getObjectPageList(params, callback) {
        getPatrolObjectPage(params).then(response => {
          this.allObjectPageList = response.data.data || []

          callback(response)
        })
      },

      // 回显
      getDetailLine(record) {
        Promise.all([
          getLine({ lineId: record.lineId }),
          getPatrolObjectPage({
            lineId: this.rowInfo.lineId,
            pageNum: 1,
            pageSize: Number.MAX_SAFE_INTEGER,
            patrolType: this.$route.meta.query.patrolType,
          }),
        ]).then(res => {
          let arr = this.dealObjData(res[0].data || [])
          this.allObjectPageList = res[1].data.data || []

          this.handleJoin(null, arr)
        })
        // getLine({ lineId: record.lineId }).then(res => {
        //   let arr = this.dealObjData(res.data || [])
        //   this.handleJoin(null, arr)
        // })
      },

      // 选中
      handleSelectRow(valObj) {
        // 全选
        if (!valObj.row) {
          if (valObj.checked) {
            valObj.records.forEach(el => {
              const obj = this.selectRow?.find(ele => ele?.id == el?.itemId)
              if (!obj) {
                this.selectRow.push(el)

                this.collapseData.forEach(ele => {
                  if (ele.objectId == el.id.split('-')[0]) {
                    ele.checkRowKeys = ele.dataSource.map(item => item.id)
                  }
                })
              }
            })
          } else {
            valObj.$table.data.forEach(el => {
              this.selectRow = this.selectRow.filter(ele => !(ele.id == el.id))

              this.collapseData.forEach(ele => {
                if (ele.objectId == el.id.split('-')[0]) {
                  ele.checkRowKeys = []
                }
              })
            })
          }
        } else {
          if (valObj.checked) {
            this.selectRow.push(valObj.row)

            this.collapseData.forEach(ele => {
              if (ele.objectId == valObj.row.id.split('-')[0]) {
                ele.checkRowKeys.push(valObj.row.id)
              }
            })
          } else {
            this.selectRow = this.selectRow.filter(el => !(el.id == valObj.row.id))

            this.collapseData.forEach(ele => {
              if (ele.objectId == valObj.row.id.split('-')[0]) {
                ele.checkRowKeys = ele.checkRowKeys.filter(el => !(el == valObj.row.id))
              }
            })
          }
        }
      },

      // 删除
      handleDelete(item) {
        this.chooseObject = this.chooseObject.filter(el => el.objectId !== item.objectId)
        let obj = this.collapseData.find(el => el.objectId === item.objectId)
        this.selectRow = this.selectRow.filter(el => !obj.dataSource.find(ele => ele.id === el.id))
        this.collapseData = this.collapseData.filter(el => el.objectId !== item.objectId)

        this.allObjectPageList = this.allObjectPageList.map(el => ({
          ...el,
          isSelect: this.chooseObject.some(ele => ele.objectId === el.objectId),
        }))
      },

      // 加入
      handleJoin(row, details) {
        let objectId
        if (row) {
          objectId = this.chooseObject
            .map(el => el.objectId)
            .concat(row.objectId)
            .join(',')
        } else {
          objectId = details.map(el => el.objectId).join(',')
        }

        if (objectId === '') {
          this.allObjectPageList = this.allObjectPageList.map(el => ({
            ...el,
            isSelect: false,
          }))

          return
        }

        getChooseObject({ objectId }).then(res => {
          this.chooseObject = res.data

          let arr = this.dealObjData(res.data || [])

          if (row) {
            arr.forEach(el => {
              if (el.objectId != row.objectId) {
                el.checkRowKeys = this.collapseData.find(ele => ele.objectId == el.objectId).checkRowKeys
              } else {
                this.selectRow.push(...el.dataSource)
              }
            })
          }

          if (details) {
            arr.forEach(el => {
              let obj = details.find(ele => ele.objectId == el.objectId)
              if (obj) {
                el.checkRowKeys = []
                el.dataSource.forEach(ele => {
                  let childObj = obj.dataSource.find(item => item.id == ele.id)
                  if (childObj) {
                    this.selectRow.push(ele)
                    el.checkRowKeys.push(ele.id)
                  }
                })
              }
            })
          }

          this.allObjectPageList = this.allObjectPageList.map(el => ({
            ...el,
            isSelect: this.chooseObject.some(ele => ele.objectId === el.objectId),
          }))

          this.collapseData = arr
        })
      },

      dealObjData(array) {
        let arr = array.map(el => {
          let targetArr = []
          let checkRowKeys = []
          el.childObject?.forEach(ele => {
            ele.patrolItemCheckeds?.forEach(item => {
              targetArr.push({ ...ele, ...item, id: `${el.objectId}-${ele.objectId}-${item.itemId}` })
              checkRowKeys.push(`${el.objectId}-${ele.objectId}-${item.itemId}`)
            })
          })

          return { ...el, dataSource: targetArr, checkRowKeys }
        })
        return arr
      },

      confirm(callback) {
        const targetArr = []
        this.selectRow.forEach((el, index) => {
          if (targetArr.some(ele => ele.objectId == el.objectId)) {
            const idx = targetArr.findIndex(item => item.objectId == el.objectId)
            targetArr[idx].itemIds.push(el.itemId)
          } else {
            targetArr.push({ objectId: el.objectId, itemIds: [el.itemId] })
          }
        })

        this.collapseData.forEach(el => {
          if (targetArr.every(ele => ele.objectId !== el.objectId)) {
            targetArr.push({ objectId: el.objectId, itemIds: [] })
          }
        })

        const params = {
          lineId: this.rowInfo.lineId,
          objects: targetArr,
        }

        callback(params)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .detail-content {
    width: 100%;
    height: 100%;
    display: flex;

    .section-item {
      flex: 1;
      position: relative;
      height: 100%;
      background-color: #fff;
      border-radius: 2px;
      overflow: hidden;
    }

    ::v-deep .ant-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      .ant-tabs-bar {
        // border-bottom: transparent;
      }
      .ant-tabs-top-content {
        height: 100%;
        width: 100%;
        flex: 1;
        display: flex;

        .ant-tabs-tabpane-active {
          height: 100%;
          width: 100%;
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
</style>
