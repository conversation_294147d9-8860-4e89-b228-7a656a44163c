<template>
  <div class="map">
    <div class="search">
      <a-select
        show-search
        allowClear
        :defaultActiveFirstOption="false"
        placeholder="请输入对象名称"
        style="width: 300px"
        :filter-option="false"
        :not-found-content="null"
        @search="onSearchChange"
        @change="handleChange"
      >
        <a-spin v-if="!objectList.length" slot="notFoundContent" size="small" />
        <a-select-option v-for="d in objectList" :key="d.objectId">
          {{ d.objectName }}
        </a-select-option>
      </a-select>
    </div>

    <MapBox :options="{}" @onMapMounted="onMapMounted" @onMapStyleLoad="onMapStyleLoad" />
    <!-- 
    <div class="legend">
      <div class="legend-item" v-for="(el, i) in allObject" :key="i">
        <div class="legend-icon" :style="{ background: legendColors[i] }"></div>
        {{ el.name }}（{{ el.data.length }}）
      </div>
    </div> -->

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import MapBox from '@/components/MapBox/index.vue'
  import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css'
  import { dealAllPoint, mapBound, mapBoundGeo } from '@/utils/mapBounds.js'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import * as turf from '@turf/turf'
  import debounce from 'lodash/debounce'
  import { getPatrolObjectList } from '../../../services'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'ModalMap',
    components: { MapBox, MapStyle },
    props: {
      rowInfo: {
        default: null,
      },
      allObjectPageList: {
        default: () => [],
      },
    },
    data() {
      return {
        mapIns: null,
        allGeoJson: null,

        rules: {
          name: [{ required: true, message: '请输入范围名称', trigger: 'blur' }],
        },
        activeObject: null,
        legendColors: ['#B040F9', '#408EF9', '#1AC95C', '#FFCE00', '#6240F9', '#00DDDF'],

        allObject: [],
        objectList: [],
      }
    },
    computed: {},
    watch: {
      rowInfo: {
        handler(newVal, oldVal) {
          this.dealLayers([newVal])
        },
        deep: true,
      },

      allObjectPageList: {
        handler(newVal) {
          this.drawMarks(newVal)
        },
        deep: true,
      },
    },
    created() {
      this.getObjectList()
    },
    methods: {
      onMapMounted(mapIns) {
        if (this.mapIns) return

        this.mapIns = mapIns

        this.dealLayers([this.rowInfo])
        this.drawMarks(this.allObjectPageList)
      },

      cancel() {
        this.$emit('cancel')
      },

      onMapStyleLoad(mapIns) {
        this.dealLayers([this.rowInfo])
        this.drawMarks(this.allObjectPageList)
      },

      // 图层
      dealLayers(list) {
        if (!this.mapIns) return
        if (!list?.length) return

        if (this.mapIns.getSource('all-area')) {
          clearSourceAndLayer(this.mapIns, ['all-area'], ['all-area', 'all-point'])
        }

        let allGeoJson = { type: 'FeatureCollection', features: [] }
        list.forEach(ele => {
          allGeoJson.features.push.apply(allGeoJson.features, ele.lineRange.features)

          allGeoJson.features.push({
            type: 'Feature',
            properties: { ...ele, lineRange: null },
            geometry: turf.centroid(ele.lineRange).geometry,
          })
        })

        this.mapIns.addSource('all-area', { type: 'geojson', data: allGeoJson })

        this.allGeoJson = allGeoJson
        mapBoundGeo(this.allGeoJson, this.mapIns, { top: 200, bottom: 200, left: 200, right: 200 })

        this.mapIns.addLayer({
          id: 'all-area',
          type: 'fill',
          source: 'all-area',
          paint: {
            'fill-color': '#00FF16',
            'fill-opacity': 0.35,
          },
          filter: ['==', '$type', 'Polygon'],
        })

        this.mapIns.addLayer({
          id: 'all-point',
          type: 'symbol',
          source: 'all-area',
          layout: {
            'text-size': 12,
            'text-field': ['get', 'lineName'],
            // 'text-offset': [0, 1.25],
            'text-anchor': 'center',
          },
          filter: ['==', '$type', 'Point'],
        })
      },

      onSearchChange: debounce(function (val) {
        this.getObjectList(val)
      }, 500),
      handleChange(val) {
        if (val === undefined) {
          this.getObjectList(val)
          return
        }
        this.activeObject = this.objectList.find(el => el.objectId == val)
        const { longitude, latitude } = this.activeObject
        if (latitude && longitude) {
          this.mapIns.flyTo({
            center: [+longitude, +latitude],
          })
        } else {
          this.$message.info('无坐标信息')
        }
      },
      drawMarks(list) {
        if (!this.mapIns) return
        if (!list?.length) return

        if (this.mapIns.getSource('all-makers')) {
          clearSourceAndLayer(this.mapIns, ['all-makers'], ['all-makers-circle'])
        }

        let allGeoJson = { type: 'FeatureCollection', features: [] }

        list.forEach(ele => {
          if (ele.latitude && ele.longitude) {
            allGeoJson.features.push({
              type: 'Feature',
              properties: ele,
              geometry: {
                type: 'Point',
                coordinates: [+ele.longitude, +ele.latitude],
              },
            })
          }
        })

        this.mapIns.addSource('all-makers', { type: 'geojson', data: allGeoJson })

        this.mapIns.addLayer({
          id: 'all-makers-circle',
          type: 'circle',
          source: 'all-makers',
          paint: {
            'circle-radius': 6,
            'circle-color': ['case', ['boolean', ['get', 'isSelect'], true], '#4264fb', '#999999'],
          },
        })

        const popup = new mapboxgl.Popup({
          closeButton: false,
          closeOnClick: false,
        })

        this.mapIns.on('mouseenter', 'all-makers-circle', e => {
          const html = `<div>${e.features[0].properties.objectName}</div>`
          const coordinates = e.features[0].geometry.coordinates.slice()
          while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
            coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360
          }

          popup.setLngLat(coordinates).setHTML(html).addTo(this.mapIns)
        })

        this.mapIns.on('mouseleave', 'all-makers-circle', () => {
          popup.remove()
        })
      },

      getObjectList(objectName) {
        getPatrolObjectList({
          objectName,
          patrolType: this.$route.meta.query.patrolType,
        }).then(res => {
          this.objectList = res.data || []
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .map {
    width: 100%;
    height: 100%;
    position: relative;
    .search {
      padding: 8px;
      border-radius: 8px;
      background-color: #fff;
      position: absolute;
      left: 10px;
      top: 10px;
      z-index: 1;
      box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.2);
    }
  }

  ::v-deep .mapboxgl-ctrl-top-left {
    display: none;
  }
  ::v-deep .mapboxgl-ctrl-bottom-right {
    // display: none;
    bottom: 80px;
  }

  ::v-deep .ant-form-item {
    margin-bottom: 18px;
  }
  ::v-deep .ant-form-item-with-help {
    margin-bottom: 5px;
  }
</style>
