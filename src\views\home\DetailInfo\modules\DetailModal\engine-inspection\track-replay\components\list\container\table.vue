<template>
  <div class="table-modal-content">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="名称">
              <a-input v-model="queryParam.objectName" placeholder="请输入" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="对象类型">
              <a-select v-model="queryParam.objectType" placeholder="请选择" :options="objectOptions" allowClear />
            </a-form-item>
          </a-col>

          <a-col :md="8" :sm="24">
            <span class="table-page-search-submitButtons" style="float: right">
              <a-button type="primary" @click="handleQuery" icon="search">查询</a-button>
              <a-button style="margin-left: 8px" icon="redo" @click="resetQuery">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class="table-box">
      <VxeTable
        ref="vxeTableRef"
        :isShowTableHeader="false"
        :columns="columns"
        :tableData="list"
        :loading="loading"
        @refresh="getList"
        :tablePage="false"
      ></VxeTable>
    </div>
  </div>
</template>
<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import * as _ from 'lodash'

  export default {
    name: 'SelectTable',
    props: {
      rowInfo: {},
      chooseObject: {},
    },
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        open: false,
        modalTitle: '',

        objectOptions: [
          { label: '水利工程', value: '1' },
          { label: '监测站点', value: '2' },
          { label: '江河湖泊', value: '3' },
          { label: '站点终端', value: '8' },
          { label: '工程设备', value: '9' },
        ],

        queryParam: {
          lineId: this.rowInfo.lineId,
          objectName: undefined,
          objectType: undefined,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        },
        total: 0,
        loading: false,

        list: [],
        allList: [],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '编码',
            field: 'objectCode',
            minWidth: 100,
          },
          {
            title: '名称',
            field: 'objectName',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '类型',
            field: 'objectType',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.objectOptions.find(el => el.value == row.objectType).label
              },
            },
          },
          {
            title: '详细地址',
            field: 'address',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleJoin(row)}>加入</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {
      chooseObject: {
        handler(newVal, oldVal) {
          this.list = this.allList.filter(el => !newVal.find(ele => ele.objectId === el.objectId))
        },
        deep: true,
      },
    },
    created() {
      this.handleQuery()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true
        const params = {
          ...this.queryParam,
        }

        this.$emit('getObjectPageList', params, res => {
          this.allList = res.data.data || []
          setTimeout(() => {
            this.list = this.allList.filter(el => !this.chooseObject.find(ele => ele.objectId === el.objectId))

            this.total = res.data.total
            this.loading = false
          }, 300)
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          objectName: undefined,
          objectType: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      handleJoin(row) {
        this.$emit('handleJoin', row)
      },
    },
  }
</script>
<style lang="scss" scoped>

  .table-modal-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    .table-box {
      flex: 1;
      position: relative;
    }
  }
</style>
