<template>
  <div class="list">
    <!-- <div class="title">巡检人员</div>
    <div class="search">
      <div class="search-item">
        任务类型：
        <a-radio-group button-style="solid" v-model="query.tabVal">
          <a-radio-button :value="el.value" v-for="(el, i) in taskTypeOptions" :key="i">{{ el.label }}</a-radio-button>
        </a-radio-group>
      </div>
      <div class="search-item">
        巡检开始时间：
        <a-date-picker
          v-model="query.taskStartTime"
          style="min-width: auto"
          format="YYYY-MM-DD HH"
          valueFormat="YYYY-MM-DD HH"
          :show-time="{ format: 'HH' }"
        />
      </div>
      <div class="search-item">
        巡检结束时间：
        <a-date-picker
          v-model="query.taskEndTime"
          style="min-width: auto"
          format="YYYY-MM-DD HH"
          valueFormat="YYYY-MM-DD HH"
          :show-time="{ format: 'HH' }"
        />
      </div>
      <div class="search-item">
        巡检人：
        <a-input v-model="query.patrolUserName" placeholder="请输入" allowClear />
      </div>
      <div class="search-item" style="justify-content: flex-end">
        <a-button type="primary" icon="search" @click="() => handleQuery()">查询</a-button>
        <a-button style="margin-left: 8px" icon="redo" @click="reset">重置</a-button>
      </div>
    </div> -->

    <div class="calendar-box">
      <a-calendar valueFormat="YYYY-MM-DD HH:mm:ss" v-model="query.calendarVal" :fullscreen="false" @select="onSelect">
        <!-- <div slot="dateCellRender" slot-scope="value" :class="[hasTask(value) ? 'date-box' : '']">
          {{ hasTask(value) ? value.date() : '' }}
        </div> -->
      </a-calendar>
    </div>

    <a-divider />
    <a-empty v-if="!dataSource.data?.length" style="margin-top: 80px" />

    <template v-else>
      <div class="list-content">
        <div
          :class="['item']"
          v-for="(el, i) in dataSource.data"
          :key="i"
          @click="() => onItemClick(el)"
          :style="i === 0 ? { marginTop: '0px' } : {}"
        >
          <div class="item-top">
            <div class="left">
              <div class="value">计划任务：{{ el?.taskName }}</div>
            </div>
          </div>
          <div class="item-open">
            所属计划：
            <div class="value">{{ el?.planName }}</div>
          </div>
          <div class="item-open">
            巡检人：
            <div class="value">{{ el?.patrolUserName }}</div>
          </div>
          <div class="item-open">
            开始时间：
            <div class="value">{{ el?.taskStartTime }}</div>
          </div>
          <div class="item-open">
            结束时间：
            <div class="value">{{ el?.taskEndTime }}</div>
          </div>
          <div class="item-open">
            巡检时长：
            <div class="value">{{ el?.patrolDuration?.toFixed(2) }} 小时</div>
          </div>
          <div class="item-open">
            轨迹长度：
            <div class="value">{{ el?.patrolMileage === null ? '-' : el?.patrolMileage }} km</div>
          </div>
          <div class="item-open">
            平均速度：
            <div class="value">{{ el?.average === null ? '-' : el?.average }} km/小时</div>
          </div>
          <div class="item-open" style="margin-top: 10px; justify-content: flex-end">
            <a-button size="small" style="margin-right: 10px" @click.stop="() => onTaskDetail(el)">任务详情</a-button>
            <a-button size="small" @click.stop="() => onReplayClick(el)">轨迹回放</a-button>
          </div>
        </div>
      </div>

      <div style="text-align: center">
        <a-pagination size="small" :total="dataSource?.total" :pageSize="5" @change="onPageChange" />
      </div>
    </template>

    <LineParamSetModal
      v-if="showLineParamsSetModal"
      ref="lineParamsSetModalRef"
      :workShiftList="workShiftList"
      :lineOptions="lineOptions"
      @close="showLineParamsSetModal = false"
    />
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'

  import LineParamSetModal from './LineParamSetModal.vue'
  import { getWorkShiftList, getPatrolLineList } from '../../../services.js'

  const taskTypeOptions = [
    { label: '全部', value: 99 },
    { label: '计划任务', value: 0 },
    { label: '临时任务', value: 1 },
  ]
  export default {
    name: 'List',
    components: { LineParamSetModal },
    props: {
      dataSource: { default: () => [] },
    },
    data() {
      return {
        showLineParamsSetModal: false,

        workShiftList: [],
        lineOptions: [],

        activeItem: {},
        query: {
          calendarVal: moment(),

          // tabVal: 99,
          taskStartTime: null,
          taskEndTime: null,
          // patrolUserName: undefined,
          pageNum: 1,
          pageSize: 5,
        },
        taskTypeOptions: taskTypeOptions,
      }
    },
    computed: {},
    watch: {},
    created() {
      getWorkShiftList({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
      }).then(res => {
        this.workShiftList = res?.data?.data
      })

      getPatrolLineList({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        patrolType: '2',
      }).then(res => {
        this.lineOptions = res?.data?.data || []
      })
    },
    mounted() {
      this.handleQuery()
    },

    methods: {
      // hasTask(val) {
      //   if (this.dataSource.length) {
      //     this.dataSource.forEach(el => {
      //       if (moment(el.taskStartTime).value() < val.value() < moment(el.taskEndTime).value()) return true
      //     })
      //   }
      //   return false
      // },

      onSelect(value, mode) {
        this.handleQuery()
      },

      onAddClick() {
        this.$emit('addClick')
      },
      onItemClick(item) {
        this.activeItem = item
        this.$emit('itemClick', item)
      },
      handleQuery(pageNum) {
        const params = {
          ...this.query,
          pageNum: pageNum || 1,
          // isTemp: this.query.tabVal == 99 ? undefined : this.query.tabVal,
          taskStartTime: this.query.calendarVal.startOf('day').format('YYYY-MM-DD HH:mm:ss') || undefined,
          taskEndTime: this.query.calendarVal.endOf('day').format('YYYY-MM-DD HH:mm:ss') || undefined,
          calendarVal: undefined,
        }

        this.$emit('getList', params)
      },
      reset() {
        this.query = {
          ...this.query,

          tabVal: 99,
          taskStartTime: null,
          taskEndTime: null,
          patrolUserName: undefined,
          pageNum: 1,
          pageSize: 5,
        }
        this.handleQuery()
      },

      onPageChange(page) {
        this.handleQuery(page)
      },

      onReplayClick(item) {
        this.$emit('getTrackDetail', item)
      },

      onTaskDetail(item) {
        this.showLineParamsSetModal = true
        this.$nextTick(() => {
          this.$refs.lineParamsSetModalRef.handleDetail(item)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

  .list {
    width: 300px;
    border-radius: 2px;
    background-color: #fff;
    height: 100%;

    .title {
      background-color: #1890ff;
      border-radius: 2px 2px 0 0;
      line-height: 20px;
      color: #fff;
      padding: 10px;
    }

    .search {
      padding: 10px;

      .search-item {
        display: flex;
        align-items: center;
        padding: 5px 0;
        white-space: nowrap;
      }
    }

    .plus {
      width: 55px;
      min-width: auto;
      padding: 0;
      text-align: center;
      margin-left: 5px;

      ::v-deep .anticon-plus {
        font-size: 14px;
        font-weight: 700;
      }
    }

    .list-content {
      margin: 10px 0;
      padding: 0 10px;
      height: calc(100% - 360px);
      overflow: auto;

      .item {
        margin-top: 10px;
        margin-bottom: 4px;
        padding-bottom: 10px;
        box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 4px;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.3s;
        overflow: hidden;

        .item-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #1890ff;
          color: #fff;
          height: 32px;
          padding-left: 5px;
        }

        .left {
          display: flex;
          align-items: center;

          .order {
            background-color: #1890ff;
            color: #fff;
            font-size: 12px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 4px;
          }

          .value {
            margin-left: 5px;
            max-width: 170px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .line {
          color: #999;
        }
      }

      .active-item {
        transition: all 0.3s;
        overflow: hidden;
      }

      .item-open {
        padding: 0 10px;
        margin-top: 5px;

        display: flex;
        align-items: center;
        white-space: nowrap;

        .value {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    ::v-deep .ant-divider-horizontal {
      margin: 0;
    }

    ::v-deep .ant-radio-button-wrapper {
      padding: 0 10px;
    }

    .calendar-box {
      width: 100%;
      height: 310px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }
    ::v-deep .ant-fullcalendar-header .ant-radio-group {
      display: none;
    }

    ::v-deep .ant-fullcalendar-content {
      bottom: auto;
      display: flex;
      align-items: center;
      top: 50%;
      transform: translateY(-50%);
    }
    .date-box {
      width: 24px;
      height: 24px;
      margin: 0 auto;
      background-color: #52c41a;
      border-radius: 50%;
      color: #fff;
    }
  }
</style>
