import request from '@/utils/request'

// 轨迹回放列表
export function getPatrolTaskList(data) {
  return request({
    url: '/patrol/task/prjstd/track/list',
    method: 'post',
    data
  })
}

// 巡检人点位详情
export function getTaskTrack(params) {
  return request({
    url: '/patrol/task/track/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检计划——线路列表
export function getPatrolLineList(data) {
  return request({
    url: '/patrol/line/page',
    method: 'post',
    data
  })
}

// 巡检计划——班次列表
export function getWorkShiftList(data) {
  return request({
    url: '/work/shift/page',
    method: 'post',
    data
  })
}