<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">渡槽代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">渡槽名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="渡槽所在位置">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-model-item label="渡槽所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="渡槽过水能力(m³/s)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.flumWatProp"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.flumWatProp }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="渡槽形式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.flumPatt"
                placeholder="请选择"
                :options="flumPattOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ flumPattOptions.find(el => el.value == data.flumPatt)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="渡槽断面形式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.flumSecPatt"
                placeholder="请选择"
                :options="flumSecPattOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ flumSecPattOptions.find(el => el.value == data.flumSecPatt)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="跨河长度(m)">
              <a-input-number v-if="type == 'edit'" v-model="form.crRvLen" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.crRvLen }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="支承形式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.suppType"
                placeholder="请选择"
                :options="suppTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ suppTypeOptions.find(el => el.value == data.suppType)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="支承孔数(孔)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.suppOrifNum"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.suppOrifNum }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getFlum, updateFlum } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},

        flumPattOptions: [], //渡槽形式
        flumSecPattOptions: [], //渡槽断面形式
        suppTypeOptions: [], //支承形式

        engStatOptions: [], //工程建设情况
        type: 'detail',
        form: {
          compDate: '',
          crRvLen: undefined,
          endLat: '',
          endLong: '',
          engStat: '',
          flumPatt: '',
          flumSecPatt: '',
          flumWatProp: undefined,
          id: undefined,
          note: '',
          projectId: undefined,
          startDate: '',
          startLat: '',
          startLong: '',
          suppOrifNum: undefined,
          suppType: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('flumPatt').then(res => {
          this.flumPattOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('flumSecPatt').then(res => {
          this.flumSecPattOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('suppType').then(res => {
          this.suppTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getFlum({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            flumPatt: res.data.flumPatt ? res.data.flumPatt : undefined,
            flumSecPatt: res.data.flumSecPatt ? res.data.flumSecPatt : undefined,
            suppType: res.data.suppType ? res.data.suppType : undefined,
            engStat: res.data.engStat ? res.data.engStat : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateFlum(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
