<template>
  <div class="basic-info">
    <div class="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">
            基本信息
            <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
              {{ this.type == 'detail' ? '编辑' : '确定' }}
            </a-button>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">闸站代码：</span>
            <span class="value">{{ data?.projectCode }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">闸站名称：</span>
            <span class="value">{{ data?.projectName }}</span>
          </div>
        </a-col>

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">行政区划：</span>
            <span class="value">{{ data?.districtFullName }}</span>
          </div>
        </a-col>
        <!-- <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">闸站所在位置：</span>
            <span class="value" :title="data.location">{{ data?.location }}</span>
          </div>
        </a-col> -->

        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">闸站经度(°)：</span>
            <span class="value">{{ data?.longitude }}</span>
          </div>
        </a-col>
        <a-col :lg="8" :md="8" :sm="24">
          <div class="item">
            <span class="label">闸站维度(°)：</span>
            <span class="value">{{ data?.latitude }}</span>
          </div>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="闸站所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
            <div class="form-value" :title="data.location">{{ data.location }}</div>
          </a-form-model-item>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title second-title">主要特征信息</div>
        </a-col>

        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          layout="horizontal"
          :labelCol="{ span: 12 }"
          :wrapperCol="{ span: 12 }"
          labelAlign="right"
        >
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="泵站类型" prop="pustType">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.pustType"
                placeholder="请选择"
                :options="pumpTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ pumpTypeOptions.find(el => el.value == data.pustType)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="装机流量(m³/s)" prop="insFlow">
              <a-input-number v-if="type == 'edit'" v-model="form.insFlow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.insFlow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="装机功率(kW)" prop="insPow">
              <a-input-number v-if="type == 'edit'" v-model="form.insPow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.insPow }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="水泵数量(台)" prop="pumpNum">
              <a-input-number v-if="type == 'edit'" v-model="form.pumpNum" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.pumpNum }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计扬程(m)" prop="desHead">
              <a-input-number v-if="type == 'edit'" v-model="form.desHead" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.desHead }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程任务" prop="engTask">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engTask"
                mode="multiple"
                placeholder="请选择"
                :options="engTaskOptions"
                :maxTagCount="1"
                :filter-option="filterOption"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{
                  engTaskOptions
                    .filter(item => data.engTask?.includes(item.value))
                    .map(item => item.label)
                    .join(', ')
                }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="水闸类型" prop="wagaType">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wagaType"
                placeholder="请选择"
                :options="sluiceTypeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ sluiceTypeOptions.find(el => el.value == data.wagaType)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="水闸用途" prop="wagaUse">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.wagaUse"
                placeholder="请选择"
                :options="sluicePurposeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ sluicePurposeOptions.find(el => el.value == data.wagaUse)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="设计最大过闸流量(m³/s)" prop="desLockDisc">
              <a-input-number
                v-if="type == 'edit'"
                style="width: 100%"
                v-model="form.desLockDisc"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desLockDisc }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="闸孔数量(孔)" prop="gaorNum">
              <a-input-number v-if="type == 'edit'" style="width: 100%" v-model="form.gaorNum" placeholder="请输入" />
              <div v-else class="form-value">{{ data.gaorNum }}</div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="闸孔总净宽(m)" prop="gaorTotNetWid">
              <a-input-number
                v-if="type == 'edit'"
                style="width: 100%"
                v-model="form.gaorTotNetWid"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.gaorTotNetWid }}</div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程等别" prop="engGrad">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="projectWaitOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectWaitOptions.find(el => el.value == data.engGrad)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程规模" prop="engScal">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="projectScaleOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ projectScaleOptions.find(el => el.value == data.engScal)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="主要建筑物级别" prop="mainBuildGrad">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.mainBuildGrad"
                placeholder="请选择"
                :options="mainBuildGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ mainBuildGradOptions.find(el => el.value == data.mainBuildGrad)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况" prop="engStat">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-form-model>
      </a-row>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getGate, updateGate } from './services'

  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},
        pumpTypeOptions: [],

        engTaskOptions: [],
        projectWaitOptions: [],
        projectScaleOptions: [],
        mainBuildGradOptions: [],
        engStatOptions: [],
        admDepOptions: [],

        sluiceTypeOptions: [],
        sluicePurposeOptions: [],
        type: 'detail',
        form: {
          admDep: '',
          compDate: '',
          desHead: undefined,
          desLockDisc: undefined,
          engGrad: undefined,
          engScal: null,
          engStat: '',
          engTask: undefined,
          gaorNum: null,
          gaorTotNetWid: undefined,
          id: undefined,
          insFlow: undefined,
          insPow: undefined,
          mainBuildGrad: '',
          note: '',
          projectId: undefined,
          pumpNum: undefined,
          pustType: undefined,
          startDate: '',
          wagaType: undefined,
          wagaUse: '',

          // projectId: undefined,

          // sluiceType: undefined,
          // sluicePurpose: undefined,
          // holeNumber: undefined,
          // totalWidth: undefined,

          // pumpType: undefined,
          // designedFlow: undefined,
          // designedPower: undefined,
          // pumpNumber: undefined,
          // headDesign: undefined,

          // unitManagement: '',
          // startWorkTime: '',
          // buildUpTime: '',
          // remarks: '',
        },
        rules: {},
      }
    },
    created() {
      // getOptions('pumpType').then(res => {
      //   this.pumpTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      // })

      this.init()
      getOptions('sluiceType').then(res => {
        this.sluiceTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      getOptions('sluicePurpose').then(res => {
        this.sluicePurposeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      this.getDataSource()
    },
    methods: {
      init() {
        //工程等别-projectWait
        getOptions('projectWait').then(res => {
          this.projectWaitOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程规模-projectScale
        getOptions('projectScale').then(res => {
          this.projectScaleOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 建筑物级别-mainBuildGrad
        getOptions('mainBuildGrad').then(res => {
          this.mainBuildGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 归口管理部门-admDep
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        //工程任务 engTaskOptions
        getOptions('engTask').then(res => {
          this.engTaskOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        //泵类型-pumpType
        getOptions('pumpType').then(res => {
          this.pumpTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      updateListFromStr(str) {
        let list = null
        if (str?.includes(',')) {
          // 如果字符串中包含逗号，按逗号分割并转换为数字数组
          list = str?.split(',')
          // list = str.split(',').map(Number)
        } else if (str) {
          // 如果字符串中不包含逗号，直接转换为单个数字的数组
          // list = [Number(str)]
          list = [str]
        }
        return list
      },
      getDataSource() {
        getGate({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            engTask: res.data?.engTask ? this.updateListFromStr(res.data?.engTask) : undefined,

            admDep: res.data.admDep ? `${res.data.admDep}` : undefined,

            pustType: res.data.pustType ? `${res.data.pustType}` : undefined,
            wagaType: res.data.wagaType ? `${res.data.wagaType}` : undefined,

            wagaUse: res.data.wagaUse ? `${res.data.wagaUse}` : undefined,

            engGrad: res.data.engGrad ? `${res.data.engGrad}` : undefined,
            engScal: res.data.engScal ? `${res.data.engScal}` : undefined,
            mainBuildGrad: res.data.mainBuildGrad ? `${res.data.mainBuildGrad}` : undefined,

            engStat: res.data.engStat ? `${res.data.engStat}` : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.engTask = this.form.engTask ? this.form.engTask.join(',') : ''
          this.form.projectId = this.projectId
          const params = { ...this.form }
          updateGate(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
