<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">圩垸代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">圩垸名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">行政区划：</span>
              <span class="value">{{ data?.districtFullName }}</span>
            </div>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="圩垸所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <div class="form-value" :title="data.location">{{ data.location }}</div>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">圩垸经度(°)：</span>
              <span class="value">{{ data?.longitude }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">圩垸维度(°)：</span>
              <span class="value">{{ data?.latitude }}</span>
            </div>
          </a-col> -->
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角经度(°)" prop="lowLeftLong">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLong }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="左下角维度(°)" prop="lowLeftLat">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.lowLeftLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.lowLeftLat }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角经度(°)" prop="upRightLong">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLong"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLong }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="右上角维度(°)" prop="upRightLat">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.upRightLat"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.upRightLat }}</div>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">圩垸所在位置：</span>
              <span class="value" :title="data.location">{{ data?.location }}</span>
            </div>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="圩垸分类" prop="poldClas">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.poldClas"
                placeholder="请选择"
                :options="poldClasOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ poldClasOptions.find(el => el.value == data.poldClas)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="平垸性质" prop="plodProp">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.plodProp"
                placeholder="请选择"
                :options="plodPropOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ plodPropOptions.find(el => el.value == data.plodProp)?.label }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计行洪流量(m³/s)" prop="decFlFlow">
              <a-input-number v-if="type == 'edit'" v-model="form.decFlFlow" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.decFlFlow }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计蓄洪量(10⁴m³)" prop="desStorCap">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.desStorCap"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.desStorCap }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="运用原则" prop="opPr">
              <a-input v-if="type == 'edit'" v-model="form.opPr" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.opPr }}</div>
            </a-form-model-item>
          </a-col>
          <!-- style="padding-left: 30px; background: red" -->
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getPolder, updatePolder } from './services'

  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},
        type: 'detail',
        attributes: {},

        poldClasOptions: [],
        plodPropOptions: [],

        form: {
          decFlFlow: undefined,
          desStorCap: undefined,
          id: undefined,
          lowLeftLat: undefined,
          lowLeftLong: undefined,
          note: '',
          opPr: '',
          plodProp: '',
          poldClas: '',
          projectId: undefined,
          upRightLat: undefined,
          upRightLong: undefined,
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('poldClas').then(res => {
          this.poldClasOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getOptions('plodProp').then(res => {
          this.plodPropOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getPolder({ projectId: this.projectId }).then(res => {
          this.data = res?.data || []
          this.form = {
            ...res.data,
            projectId: this.projectId,
            poldClas: res.data?.poldClas ? res.data?.poldClas : undefined,
            plodProp: res.data?.plodProp ? res.data?.plodProp : undefined,
          }
        })
      },

      onBtnClick() {
        if (this.type == 'edit') {
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updatePolder(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
