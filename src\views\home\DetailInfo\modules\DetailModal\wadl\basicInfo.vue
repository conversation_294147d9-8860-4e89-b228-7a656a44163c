<template>
  <div class="basic-info">
    <div class="content">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :labelCol="{ span: 12 }"
        :wrapperCol="{ span: 12 }"
        labelAlign="right"
      >
        <a-row :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">
              基本信息
              <a-button style="margin-left: 10px" type="primary" @click="onBtnClick" :loading="loading">
                {{ this.type == 'detail' ? '编辑' : '确定' }}
              </a-button>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">引调水代码：</span>
              <span class="value">{{ data?.projectCode }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <span class="label">引调水名称：</span>
              <span class="value">{{ data?.projectName }}</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="行政区划">
              <div class="form-value">{{ data.districtFullName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="起点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.startLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.startLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点经度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLong" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLong }}</div>
              <!-- <div class="form-value">{{ data.longitude }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="终点维度(°)">
              <a-input-number v-if="type == 'edit'" v-model="form.endLat" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.endLat }}</div>
              <!-- <div class="form-value">{{ data.latitude }}</div> -->
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="起点所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-input
                v-if="type == 'edit'"
                class="area-text"
                v-model="form.startLoc"
                placeholder="请输入"
                allowClear
              />
              <div v-else class="form-value" :title="data.startLoc">{{ data.startLoc }}</div>
              <!-- <div class="form-value">{{ data.location }}</div> -->
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="终点所在位置" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-input v-if="type == 'edit'" class="area-text" v-model="form.endLoc" placeholder="请输入" allowClear />
              <div v-else class="form-value" :title="data.endLoc">{{ data.endLoc }}</div>
              <!-- <div class="form-value">{{ data.location }}</div> -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title second-title">主要特征信息</div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程等别">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engGrad"
                placeholder="请选择"
                :options="engGradOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">{{ engGradOptions.find(el => el.value == data.engGrad)?.label }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程规模">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engScal"
                placeholder="请选择"
                :options="engScalOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engScalOptions.find(el => el.value == data.engScal)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="调入水资源分区">
              <a-input v-if="type == 'edit'" v-model="form.toWrzName" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.toWrzName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="调出水资源分区">
              <a-input v-if="type == 'edit'" v-model="form.fromWrzName" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.fromWrzName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="调入流域">
              <a-input v-if="type == 'edit'" v-model="form.toBasName" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.toBasName }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="调出流域">
              <a-input v-if="type == 'edit'" v-model="form.fromBasName" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.fromBasName }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程任务" prop="engTask">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engTask"
                :filter-option="filterOption"
                mode="multiple"
                placeholder="请选择"
                :options="engTaskOptions"
                :maxTagCount="1"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{
                  engTaskOptions
                    .filter(item => data.engTask?.includes(item.value))
                    .map(item => item.label)
                    .join(', ')
                }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程范围输水线路区">
              <a-input v-if="type == 'edit'" v-model="form.proWatTraLine" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.proWatTraLine }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="工程范围受水区">
              <a-input v-if="type == 'edit'" v-model="form.proRangWatRec" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.proRangWatRec }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="输水干线总长度(km)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.trlnTotLen"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.trlnTotLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="输水支线总长度(km)">
              <a-input-number
                v-if="type == 'edit'"
                v-model="form.cvlnTotLen"
                style="width: 100%"
                placeholder="请输入"
              />
              <div v-else class="form-value">{{ data.cvlnTotLen }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="设计年引水量(10⁴m³)">
              <a-input-number v-if="type == 'edit'" v-model="form.desAnnDiv" style="width: 100%" placeholder="请输入" />
              <div v-else class="form-value">{{ data.desAnnDiv }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="引调水方式">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.ditrMode"
                placeholder="请选择"
                :options="ditrModeOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ ditrModeOptions.find(el => el.value == data.ditrMode)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="工程建设情况">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.engStat"
                placeholder="请选择"
                :options="engStatOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ engStatOptions.find(el => el.value == data.engStat)?.label }}
              </div>
            </a-form-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="开工时间" prop="startDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.startDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.startDate }}</div>
            </a-form-model-item>
          </a-col>

          <a-col :lg="8" :md="8" :sm="24">
            <a-form-model-item label="建成时间" prop="compDate">
              <a-date-picker
                v-if="type == 'edit'"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model="form.compDate"
                placeholder="请选择"
                style="width: 100%"
              />
              <div v-else class="form-value">{{ data.compDate }}</div>
            </a-form-model-item>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <a-form-item label="归口管理部门" prop="admDep">
              <a-select
                v-if="type == 'edit'"
                allowClear
                v-model="form.admDep"
                placeholder="请选择"
                :options="admDepOptions"
                style="width: 100%"
              ></a-select>
              <div v-else class="form-value">
                {{ admDepOptions.find(el => el.value == data.admDep)?.label }}
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" prop="note" :labelCol="{ span: 3 }" :wrapperCol="{ span: 13 }" class="note">
              <a-textarea v-if="type == 'edit'" class="area-text" v-model="form.note" placeholder="请输入" allowClear />
              <div v-else class="form-value">{{ data.note }}</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getWadl, updateWadl } from './services'
  import { updateListFromStr } from '@/utils/util.js'
  export default {
    name: 'BasicInfo',
    components: {},
    props: {
      projectId: {},
    },
    data() {
      return {
        loading: false,
        data: {},
        engGradOptions: [], //工程等别
        engScalOptions: [], //工程规模
        engTaskOptions: [], //工程任务
        ditrModeOptions: [], //引调水方式

        engStatOptions: [], //工程建设情况
        admDepOptions: [], //归口管理部门
        type: 'detail',
        form: {
          crOverType: '',
          desIrrArea: undefined,
          engScal: '',
          id: undefined,
          irrRang: '',
          lowLeftLat: '',
          lowLeftLong: '',
          majWasoType: '',
          note: '',
          projectId: undefined,
          supWasoType: '',
          upRightLat: '',
          upRightLong: '',
        },
        rules: {},
      }
    },
    created() {
      this.init()
      this.getDataSource()
    },
    methods: {
      init() {
        getOptions('wadlEngGrad').then(res => {
          this.engGradOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('wadlEngTask').then(res => {
          this.engTaskOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        getOptions('wadlDitrMode').then(res => {
          this.ditrModeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getOptions('wadlEngScal').then(res => {
          this.engScalOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 归口管理部门-admDep
        getOptions('admDep').then(res => {
          this.admDepOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
        // 工程建设情况-engStat
        getOptions('engStat').then(res => {
          this.engStatOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })
      },
      getDataSource() {
        getWadl({ projectId: this.projectId }).then(res => {
          this.data = res.data
          this.form = {
            ...res.data,
            projectId: this.projectId,
            engTask: res.data?.engTask ? updateListFromStr(res.data?.engTask) : undefined,
            engGrad: res.data?.engGrad ? res.data?.engGrad : undefined,
            engScal: res.data?.engScal ? res.data?.engScal : undefined,
            admDep: res.data?.admDep ? res.data?.admDep : undefined,
            engStat: res.data?.engStat ? res.data?.engStat : undefined,
            ditrMode: res.data?.ditrMode ? res.data?.ditrMode : undefined,
          }
        })
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      onBtnClick() {
        if (this.type == 'edit') {
          this.form.engTask = this.form.engTask ? this.form.engTask.join(',') : ''
          this.form.projectId = this.projectId
          const params = { ...this.form }

          updateWadl(params)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.getDataSource()
            })
            .finally(() => (this.loading = false))
        }

        this.type = this.type == 'detail' ? 'edit' : 'detail'
      },
    },
  }
</script>

<style lang="scss" scoped>
 

 
</style>
